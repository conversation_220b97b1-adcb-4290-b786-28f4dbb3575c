/* Global Styles */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Leaflet Container */
.leaflet-container {
  height: 100vh;
  width: 100%;
  font-family: inherit;
}

/* Custom Marker Styles */
.custom-marker {
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.custom-marker:hover {
  transform: scale(1.2);
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  /* Header adjustments for mobile */
  .weather-header {
    flex-direction: column !important;
    gap: 10px !important;
    padding: 15px !important;
    text-align: center !important;
  }

  .weather-header h1 {
    font-size: 1.2rem !important;
  }

  .weather-header p {
    font-size: 0.8rem !important;
  }

  /* Stats button for mobile */
  .stats-button {
    font-size: 0.8rem !important;
    padding: 6px 12px !important;
  }

  /* Legend adjustments */
  .map-legend {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
    font-size: 0.8rem !important;
  }

  /* Popup adjustments */
  .leaflet-popup-content {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  /* Statistics dashboard mobile */
  .stats-dashboard {
    margin: 10px !important;
    padding: 20px !important;
    max-height: 85vh !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
  }

  .stats-filters {
    grid-template-columns: 1fr !important;
    gap: 10px !important;
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .weather-header {
    padding: 10px !important;
  }

  .weather-header h1 {
    font-size: 1rem !important;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
  }

  .map-legend {
    font-size: 0.7rem !important;
    padding: 10px !important;
  }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .weather-header {
    padding: 15px 25px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .custom-marker {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print Styles */
@media print {
  .weather-header,
  .map-legend,
  .stats-button {
    display: none !important;
  }

  .leaflet-container {
    height: 80vh !important;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .custom-marker,
  .spinner {
    animation: none !important;
    transition: none !important;
  }
}

/* Focus Styles for Keyboard Navigation */
button:focus,
select:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .weather-header {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
  }

  .map-legend {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .weather-header {
    background-color: rgba(44, 62, 80, 0.95) !important;
    color: white !important;
  }

  .map-legend {
    background-color: rgba(44, 62, 80, 0.95) !important;
    color: white !important;
  }

  .stats-dashboard {
    background-color: #2c3e50 !important;
    color: white !important;
  }
}
