import { useState, useMemo } from 'react';

export default function StatsDashboard({ weatherEvents, onClose }) {
  const [selectedDistrict, setSelectedDistrict] = useState('all');
  const [selectedEventType, setSelectedEventType] = useState('all');

  // Calculate statistics
  const stats = useMemo(() => {
    const filteredEvents = weatherEvents.filter(event => {
      const districtMatch = selectedDistrict === 'all' || event.district === selectedDistrict;
      const typeMatch = selectedEventType === 'all' || event.type === selectedEventType;
      return districtMatch && typeMatch;
    });

    // Event type counts
    const eventTypeCounts = {};
    filteredEvents.forEach(event => {
      eventTypeCounts[event.type] = (eventTypeCounts[event.type] || 0) + 1;
    });

    // District counts
    const districtCounts = {};
    filteredEvents.forEach(event => {
      districtCounts[event.district] = (districtCounts[event.district] || 0) + 1;
    });

    // Recent events (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentEvents = filteredEvents.filter(event => {
      // Simple date parsing - you might need to adjust based on your date format
      const eventDate = new Date(event.timestamp);
      return eventDate >= sevenDaysAgo;
    });

    return {
      totalEvents: filteredEvents.length,
      eventTypeCounts,
      districtCounts,
      recentEvents: recentEvents.length,
      mostAffectedDistrict: Object.keys(districtCounts).reduce((a, b) => 
        districtCounts[a] > districtCounts[b] ? a : b, Object.keys(districtCounts)[0]
      ),
      mostCommonEventType: Object.keys(eventTypeCounts).reduce((a, b) => 
        eventTypeCounts[a] > eventTypeCounts[b] ? a : b, Object.keys(eventTypeCounts)[0]
      )
    };
  }, [weatherEvents, selectedDistrict, selectedEventType]);

  // Get unique districts and event types for filters
  const districts = [...new Set(weatherEvents.map(event => event.district))].sort();
  const eventTypes = [...new Set(weatherEvents.map(event => event.type))].sort();

  // Export data as CSV
  const exportToCSV = () => {
    const filteredEvents = weatherEvents.filter(event => {
      const districtMatch = selectedDistrict === 'all' || event.district === selectedDistrict;
      const typeMatch = selectedEventType === 'all' || event.type === selectedEventType;
      return districtMatch && typeMatch;
    });

    const headers = ['ID', 'Type', 'District', 'Latitude', 'Longitude', 'Timestamp', 'Description', 'Reporter'];
    const csvContent = [
      headers.join(','),
      ...filteredEvents.map(event => [
        event.id,
        event.type,
        event.district,
        event.lat,
        event.lng,
        event.timestamp,
        `"${event.description.replace(/"/g, '""')}"`, // Escape quotes
        event.reporter
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `bhutan-weather-events-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 2000,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '30px',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        width: '100%'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '25px',
          borderBottom: '2px solid #eee',
          paddingBottom: '15px'
        }}>
          <h2 style={{ margin: 0, color: '#2c3e50' }}>Weather Events Statistics</h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#7f8c8d'
            }}
          >
            ×
          </button>
        </div>

        {/* Filters */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px',
          marginBottom: '25px',
          padding: '15px',
          backgroundColor: '#f8f9fa',
          borderRadius: '6px'
        }}>
          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', color: '#2c3e50' }}>
              District:
            </label>
            <select
              value={selectedDistrict}
              onChange={(e) => setSelectedDistrict(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ddd'
              }}
            >
              <option value="all">All Districts</option>
              {districts.map(district => (
                <option key={district} value={district}>{district}</option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', color: '#2c3e50' }}>
              Event Type:
            </label>
            <select
              value={selectedEventType}
              onChange={(e) => setSelectedEventType(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ddd'
              }}
            >
              <option value="all">All Event Types</option>
              {eventTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div style={{ display: 'flex', alignItems: 'end' }}>
            <button
              onClick={exportToCSV}
              style={{
                backgroundColor: '#27ae60',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                width: '100%'
              }}
            >
              📊 Export CSV
            </button>
          </div>
        </div>

        {/* Key Statistics */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '15px',
          marginBottom: '25px'
        }}>
          <div style={{
            backgroundColor: '#3498db',
            color: 'white',
            padding: '20px',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold' }}>{stats.totalEvents}</div>
            <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>Total Events</div>
          </div>

          <div style={{
            backgroundColor: '#e74c3c',
            color: 'white',
            padding: '20px',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold' }}>{stats.recentEvents}</div>
            <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>Last 7 Days</div>
          </div>

          <div style={{
            backgroundColor: '#f39c12',
            color: 'white',
            padding: '20px',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
              {stats.mostCommonEventType || 'N/A'}
            </div>
            <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>Most Common</div>
          </div>

          <div style={{
            backgroundColor: '#9b59b6',
            color: 'white',
            padding: '20px',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
              {stats.mostAffectedDistrict || 'N/A'}
            </div>
            <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>Most Affected</div>
          </div>
        </div>

        {/* Event Type Breakdown */}
        <div style={{ marginBottom: '25px' }}>
          <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>Event Types</h3>
          <div style={{ display: 'grid', gap: '8px' }}>
            {Object.entries(stats.eventTypeCounts).map(([type, count]) => (
              <div key={type} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '10px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px'
              }}>
                <span style={{ fontWeight: 'bold' }}>{type}</span>
                <span style={{
                  backgroundColor: '#3498db',
                  color: 'white',
                  padding: '4px 12px',
                  borderRadius: '20px',
                  fontSize: '0.9rem'
                }}>
                  {count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* District Breakdown */}
        <div>
          <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>Districts</h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
            gap: '8px',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {Object.entries(stats.districtCounts).map(([district, count]) => (
              <div key={district} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                fontSize: '0.9rem'
              }}>
                <span>{district}</span>
                <span style={{
                  backgroundColor: '#e74c3c',
                  color: 'white',
                  padding: '2px 8px',
                  borderRadius: '12px',
                  fontSize: '0.8rem'
                }}>
                  {count}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
