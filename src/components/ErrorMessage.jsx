export default function ErrorMessage({ message, onRetry }) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      backgroundColor: '#f8f9fa',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: '#fff',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <div style={{
          fontSize: '3rem',
          color: '#e74c3c',
          marginBottom: '20px'
        }}>
          ⚠️
        </div>
        
        <h2 style={{
          color: '#2c3e50',
          marginBottom: '15px',
          fontSize: '1.5rem'
        }}>
          Something went wrong
        </h2>
        
        <p style={{
          color: '#7f8c8d',
          marginBottom: '25px',
          lineHeight: '1.5'
        }}>
          {message}
        </p>
        
        {onRetry && (
          <button
            onClick={onRetry}
            style={{
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '4px',
              fontSize: '1rem',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#2980b9'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#3498db'}
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}
