// src/components/Legend.js
export default function Legend({ types }) {
  return (
    <div className="legend">
      <h4>Event Legend</h4>
      {types.map((t) => (
        <div key={t} className="legend-item">
          <img
            src={`/icons/${t.toLowerCase()}.png`}
            alt={t}
            width={24}
            height={24}
          />
          <span>{t}</span>
        </div>
      ))}
    </div>
  );
}


