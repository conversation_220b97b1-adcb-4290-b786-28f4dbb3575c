export default function MapLegend() {
  const eventTypes = [
    { type: 'Flood', color: '#FF6B6B', icon: '🌊' },
    { type: 'Landslide', color: '#4ECDC4', icon: '⛰️' },
    { type: 'Drought', color: '#FFE66D', icon: '☀️' },
    { type: 'Storm', color: '#A8E6CF', icon: '⛈️' },
    { type: 'Earthquake', color: '#FF8B94', icon: '🌍' },
  ];

  return (
    <div style={{
      position: 'absolute',
      bottom: '20px',
      left: '20px',
      zIndex: 1000,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      padding: '15px',
      borderRadius: '8px',
      border: '1px solid #ddd',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      minWidth: '200px'
    }}>
      <h4 style={{
        margin: '0 0 10px 0',
        fontSize: '1rem',
        color: '#2c3e50'
      }}>
        Event Types
      </h4>
      
      {eventTypes.map(({ type, color, icon }) => (
        <div key={type} style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '8px',
          fontSize: '0.9rem'
        }}>
          <div style={{
            width: '16px',
            height: '16px',
            backgroundColor: color,
            borderRadius: '50%',
            marginRight: '8px',
            border: '2px solid white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
          }}></div>
          <span style={{ marginRight: '5px' }}>{icon}</span>
          <span style={{ color: '#2c3e50' }}>{type}</span>
        </div>
      ))}
      
      <hr style={{
        margin: '12px 0 8px 0',
        border: 'none',
        borderTop: '1px solid #eee'
      }} />
      
      <div style={{
        display: 'flex',
        alignItems: 'center',
        fontSize: '0.85rem',
        color: '#7f8c8d'
      }}>
        <div style={{
          width: '20px',
          height: '3px',
          background: 'repeating-linear-gradient(to right, #FF6B6B 0, #FF6B6B 5px, transparent 5px, transparent 10px)',
          marginRight: '8px'
        }}></div>
        <span>Districts with active events</span>
      </div>
    </div>
  );
}
