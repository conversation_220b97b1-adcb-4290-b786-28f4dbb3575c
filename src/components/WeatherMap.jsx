import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { fetchWeatherEvents } from "../services/secureDataService";
import LoadingSpinner from "./LoadingSpinner";
import ErrorMessage from "./ErrorMessage";
import MapLegend from "./MapLegend";
import StatsDashboard from "./StatsDashboard";

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different weather event types
const createCustomIcon = (type) => {
  const colors = {
    'Flood': '#FF6B6B',
    'Landslide': '#4ECDC4',
    'Drought': '#FFE66D',
    'Storm': '#A8E6CF',
    'Earthquake': '#FF8B94',
    'Default': '#74B9FF'
  };

  return L.divIcon({
    className: 'custom-marker',
    html: `<div style="
      background-color: ${colors[type] || colors.Default};
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    "></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
};

export default function WeatherMap() {
  const [districts, setDistricts] = useState(null);
  const [weatherEvents, setWeatherEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [showStats, setShowStats] = useState(false);

  // Load GeoJSON data
  useEffect(() => {
    const loadGeoJSON = async () => {
      try {
        const response = await fetch("/maps/bhutan.geojson");
        if (!response.ok) {
          throw new Error(`Failed to load map data: ${response.status}`);
        }
        const data = await response.json();
        setDistricts(data);
      } catch (err) {
        console.error("Error loading GeoJSON:", err);
        setError("Failed to load map boundaries. Please refresh the page.");
      }
    };

    loadGeoJSON();
  }, []);

  // Load weather events data
  useEffect(() => {
    const loadWeatherEvents = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const events = await fetchWeatherEvents();
        setWeatherEvents(events);
        setLastUpdated(new Date());
        
      } catch (err) {
        console.error("Error loading weather events:", err);
        setError("Failed to load weather events. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadWeatherEvents();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(loadWeatherEvents, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  // District styling function
  const getDistrictStyle = (feature) => {
    const districtName = feature.properties.NAME_1;
    const hasEvents = weatherEvents.some(event => 
      event.district.toLowerCase() === districtName.toLowerCase()
    );

    return {
      color: hasEvents ? "#FF6B6B" : "#3388FF",
      weight: hasEvents ? 3 : 2,
      fillColor: hasEvents ? "#FFE6E6" : "#E6F3FF",
      fillOpacity: hasEvents ? 0.6 : 0.4,
      dashArray: hasEvents ? "5, 5" : null
    };
  };

  // District popup content
  const onEachDistrict = (feature, layer) => {
    const districtName = feature.properties.NAME_1;
    const districtEvents = weatherEvents.filter(event => 
      event.district.toLowerCase() === districtName.toLowerCase()
    );

    const popupContent = `
      <div style="min-width: 200px;">
        <h3 style="margin: 0 0 10px 0; color: #2c3e50;">${districtName}</h3>
        <p style="margin: 5px 0;"><strong>Active Events:</strong> ${districtEvents.length}</p>
        ${districtEvents.length > 0 ? `
          <div style="margin-top: 10px;">
            <strong>Recent Events:</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
              ${districtEvents.slice(0, 3).map(event => 
                `<li style="margin: 2px 0;">${event.type} - ${event.timestamp}</li>`
              ).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;

    layer.bindPopup(popupContent);
  };

  if (loading && weatherEvents.length === 0) {
    return <LoadingSpinner message="Loading weather events..." />;
  }

  if (error && !districts) {
    return <ErrorMessage message={error} onRetry={() => window.location.reload()} />;
  }

  return (
    <div style={{ position: 'relative', height: '100vh', width: '100%' }}>
      {/* Header */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        background: 'rgba(255, 255, 255, 0.95)',
        padding: '10px 20px',
        borderBottom: '1px solid #ddd',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '1.5rem', color: '#2c3e50' }}>
            Bhutan Weather Events Monitor
          </h1>
          <p style={{ margin: '2px 0 0 0', fontSize: '0.9rem', color: '#7f8c8d' }}>
            Real-time weather event tracking across all 20 districts
          </p>
        </div>
        <div style={{ textAlign: 'right', display: 'flex', alignItems: 'center', gap: '15px' }}>
          <button
            onClick={() => setShowStats(true)}
            style={{
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            📊 Statistics
          </button>
          <div>
            <div style={{ fontSize: '0.9rem', color: '#7f8c8d' }}>
              Active Events: <strong style={{ color: '#e74c3c' }}>{weatherEvents.length}</strong>
            </div>
            {lastUpdated && (
              <div style={{ fontSize: '0.8rem', color: '#95a5a6' }}>
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Map */}
      <MapContainer
        center={[27.5, 90.5]}
        zoom={8.5}
        style={{ height: '100%', width: '100%', paddingTop: '80px' }}
      >
        <TileLayer
          attribution="&copy; OpenStreetMap contributors"
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* District boundaries */}
        {districts && (
          <GeoJSON
            data={districts}
            style={getDistrictStyle}
            onEachFeature={onEachDistrict}
          />
        )}

        {/* Weather event markers */}
        {weatherEvents.map(event => (
          <Marker 
            key={event.id} 
            position={[event.lat, event.lng]}
            icon={createCustomIcon(event.type)}
          >
            <Popup>
              <div style={{ minWidth: '250px' }}>
                <h3 style={{ margin: '0 0 10px 0', color: '#e74c3c' }}>
                  {event.type}
                </h3>
                <p><strong>District:</strong> {event.district}</p>
                <p><strong>Time:</strong> {event.timestamp}</p>
                <p><strong>Description:</strong> {event.description}</p>
                {event.reporter && (
                  <p><strong>Reported by:</strong> {event.reporter}</p>
                )}
                {event.photo && (
                  <div style={{ marginTop: '10px' }}>
                    <img 
                      src={event.photo} 
                      alt="Event photo"
                      style={{ 
                        maxWidth: '100%', 
                        maxHeight: '200px',
                        borderRadius: '4px'
                      }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  </div>
                )}
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>

      {/* Legend */}
      <MapLegend />

      {/* Loading overlay */}
      {loading && weatherEvents.length > 0 && (
        <div style={{
          position: 'absolute',
          top: '90px',
          right: '20px',
          zIndex: 1000,
          background: 'rgba(255, 255, 255, 0.9)',
          padding: '10px',
          borderRadius: '4px',
          border: '1px solid #ddd'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div className="spinner" style={{
              width: '16px',
              height: '16px',
              border: '2px solid #f3f3f3',
              borderTop: '2px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}></div>
            <span style={{ fontSize: '0.9rem' }}>Updating...</span>
          </div>
        </div>
      )}

      {/* Error notification */}
      {error && (
        <div style={{
          position: 'absolute',
          top: '90px',
          left: '20px',
          right: '20px',
          zIndex: 1000,
          background: '#fee',
          color: '#c33',
          padding: '10px',
          borderRadius: '4px',
          border: '1px solid #fcc'
        }}>
          {error}
        </div>
      )}

      {/* Statistics Dashboard */}
      {showStats && (
        <StatsDashboard
          weatherEvents={weatherEvents}
          onClose={() => setShowStats(false)}
        />
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
