/**
 * Secure Data Service for Bhutan Weather Events
 * Handles Google Sheets integration with proper error handling and validation
 */

// Configuration
const CONFIG = {
  SHEET_ID: import.meta.env.VITE_GOOGLE_SHEET_ID || '1sGmoJtrhUgX3zDpmBBF-P8Tl6qr1lVtbmamA4PLQ2BY',
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
};

// Simple cache implementation
const cache = new Map();

/**
 * Validates weather event data structure
 */
function validateWeatherEvent(row) {
  if (!Array.isArray(row) || row.length < 8) {
    return false;
  }

  const [id, type, district, coordinates, timestamp, description, photo, reporter] = row;
  
  // Basic validation
  if (!id || !type || !district || !coordinates) {
    return false;
  }

  // Validate coordinates format (lat,lng)
  const coordPattern = /^-?\d+\.?\d*,-?\d+\.?\d*$/;
  if (!coordPattern.test(coordinates)) {
    return false;
  }

  return true;
}

/**
 * Sanitizes text input to prevent XSS
 */
function sanitizeText(text) {
  if (typeof text !== 'string') return String(text || '');
  
  return text
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim();
}

/**
 * Formats Google Sheets date
 */
function formatGoogleDate(dateString) {
  if (!dateString || !dateString.startsWith("Date(")) return dateString;

  try {
    const parts = dateString
      .replace("Date(", "")
      .replace(")", "")
      .split(",")
      .map(Number);

    const [year, month, day, hour = 0, minute = 0] = parts;
    const date = new Date(year, month, day, hour, minute);

    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }

    return date.toLocaleString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true
    });
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return dateString;
  }
}

/**
 * Fetches data from Google Sheets with retry logic
 */
async function fetchWithRetry(url, retries = CONFIG.MAX_RETRIES) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.warn(`Fetch attempt ${i + 1} failed:`, error.message);
      
      if (i === retries - 1) throw error;
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * (i + 1)));
    }
  }
}

/**
 * Main function to fetch and process Google Sheets data
 */
export async function fetchWeatherEvents(sheetId = CONFIG.SHEET_ID) {
  try {
    // Check cache first
    const cacheKey = `weather_events_${sheetId}`;
    const cached = cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CONFIG.CACHE_DURATION) {
      console.log('Returning cached weather events data');
      return cached.data;
    }

    // Validate sheet ID
    if (!sheetId || typeof sheetId !== 'string' || sheetId.length < 10) {
      throw new Error('Invalid Google Sheet ID');
    }

    const url = `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:json`;
    
    console.log('Fetching weather events from Google Sheets...');
    const response = await fetchWithRetry(url);
    const text = await response.text();

    // Validate response format
    if (!text.includes('google.visualization.Query.setResponse')) {
      throw new Error('Invalid Google Sheets response format');
    }

    // Parse Google's JSONP response safely
    const jsonStart = text.indexOf('(') + 1;
    const jsonEnd = text.lastIndexOf(')');
    
    if (jsonStart === 0 || jsonEnd === -1) {
      throw new Error('Could not parse Google Sheets response');
    }

    const jsonString = text.substring(jsonStart, jsonEnd);
    const json = JSON.parse(jsonString);
    
    // Validate data structure
    if (!json.table || !json.table.rows) {
      throw new Error('Invalid data structure from Google Sheets');
    }

    // Process and validate rows
    const rawRows = json.table.rows.map(r => 
      r.c.map(cell => cell ? cell.v : "")
    );

    const validEvents = rawRows
      .filter(validateWeatherEvent)
      .map(row => {
        const [id, type, district, coordinates, timestamp, description, photo, reporter] = row;
        const [lat, lng] = coordinates.split(",").map(Number);

        return {
          id: sanitizeText(id),
          type: sanitizeText(type),
          district: sanitizeText(district),
          lat: Number(lat),
          lng: Number(lng),
          timestamp: formatGoogleDate(timestamp),
          description: sanitizeText(description),
          photo: sanitizeText(photo),
          reporter: sanitizeText(reporter),
          lastUpdated: new Date().toISOString()
        };
      });

    console.log(`Successfully processed ${validEvents.length} valid weather events`);

    // Cache the results
    cache.set(cacheKey, {
      data: validEvents,
      timestamp: Date.now()
    });

    return validEvents;

  } catch (error) {
    console.error('Error fetching weather events:', error);
    
    // Return cached data if available, even if expired
    const cacheKey = `weather_events_${sheetId}`;
    const cached = cache.get(cacheKey);
    if (cached) {
      console.warn('Returning expired cached data due to fetch error');
      return cached.data;
    }
    
    throw error;
  }
}

/**
 * Clears the cache (useful for testing or manual refresh)
 */
export function clearCache() {
  cache.clear();
  console.log('Weather events cache cleared');
}

export default fetchWeatherEvents;
