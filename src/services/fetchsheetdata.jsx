export async function fetchGoogleSheet(sheetId) {
  const url = `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:json`;
  const res = await fetch(url);
  const text = await res.text();

  // Parse Google’s weird JSON
  const json = JSON.parse(text.substr(47).slice(0, -2));
  console.log("Parsed Google JSON:", json);

  // Extract only the rows as arrays
  const rows = json.table.rows.map(r => r.c.map(cell => cell ? cell.v : ""));
  return rows;
}


export default fetchGoogleSheet;
