
import L from "leaflet";

// map event type → icon file name
const iconMap = {
  HeavyRain: "heavy-rain.png",
  Thunderstorm: "thunderstorm.png",
  Flood: "water.png",
  Landslide: "landslide.png",
  StrongWind: "wind.png",
  Hailstorm: "hailstone.png",
  Snowfall: "snowfall.png",
};

export function getEventIcon(type) {
  const key = type.toLowerCase().trim();
  const fileName = iconMap[key] || "default.png"; // fallback icon

  return L.icon({
    iconUrl: `/icons/${fileName}`,
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -28]
  });
}

export const availableEventTypes = Object.keys(iconMap);
