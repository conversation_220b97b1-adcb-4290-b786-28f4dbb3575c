
import L from "leaflet";

// map event type → icon file name
const iconMap = {
  'heavy rain': "heavy-rain.png",
  'thunderstorm': "thunderstorm.png",
  'flood': "water.png",
  'landslide': "landslide.png",
  'strong wind': "wind.png",
  'hailstorm': "hailstone.png",
  'snowfall': "snowfall.png",
};

export function getEventIcon(type) {
  const key = type.toLowerCase().trim();
  const fileName = iconMap[key] || "default.png"; // fallback icon

  return L.icon({
    iconUrl: `/icons/${fileName}`,
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -28]
  });
}

export const availableEventTypes = Object.keys(iconMap);
