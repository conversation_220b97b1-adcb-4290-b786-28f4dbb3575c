import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eoJSON } from "react-leaflet";
import "leaflet/dist/leaflet.css";

export default function App() {
  const [districts, setDistricts] = useState(null);

  useEffect(() => {
  fetch("/maps/bhutan.geojson")
    .then((res) => res.json())
    .then((data) => {
      console.log("Loaded GeoJSON:", data);  // ✅ debug here
      setDistricts(data);
    }); 
  }, []);


  return (
    <MapContainer center={[27.5, 90.5]} zoom={8} style={{ height: "100vh", width: "100%" }}>
      <TileLayer
        attribution="&copy; OpenStreetMap contributors"
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
  </MapContainer>

  );
}
