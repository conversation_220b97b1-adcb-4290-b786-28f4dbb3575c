import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import "./App.css";
import { fetchGoogleSheet } from "./services/fetchsheetdata";

export default function App() {
  const [districts, setDistricts] = useState(null);
  const [weatherData, setWeatherData] = useState(null);

  // 1. Load GeoJSON
  useEffect(() => {
    fetch("/maps/bhutan.geojson")
      .then((res) => res.json())
      .then((data) => {
        console.log("Loaded GeoJSON:", data);
        setDistricts(data);
      })
      .catch((error) => {
        console.error("Error loading GeoJSON:", error);
      });
  }, []);

  function formatGoogleDate(dateString) {
  if (!dateString || !dateString.startsWith("Date(")) return dateString;

  // Extract numbers from inside Date(...)
  const parts = dateString
    .replace("Date(", "")
    .replace(")", "")
    .split(",")
    .map(Number);

  const [year, month, day, hour, minute] = parts;

  const date = new Date(year, month, day, hour, minute);

  // Format into "3 Sep 2025, 10:21 AM"
  return date.toLocaleString("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true
  });
}


  // 2. Load Google Sheets
  useEffect(() => {
  fetchGoogleSheet("1sGmoJtrhUgX3zDpmBBF-P8Tl6qr1lVtbmamA4PLQ2BY").then(rows => {
    if (!rows) return;

    const parsedEvents = rows.map(row => {
  const [lat, lng] = row[3].split(",").map(Number);
  return {
    id: row[0],
    type: row[1],
    district: row[2],
    lat,
    lng,
    timestamp: formatGoogleDate(row[4]),   // 👈 format the timestamp here
    description: row[5],
    photo: row[6],
    reporter: row[7]
  };
});


    setWeatherData(parsedEvents);
  });
}, []);


  return (
    <MapContainer
      center={[27.5, 90.5]}
      zoom={8.5}
      style={{ height: "100vh", width: "100%" }}
    >
      <TileLayer
        attribution="&copy; OpenStreetMap contributors"
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      {/* Bhutan districts */}
      {districts && (
        <GeoJSON
          data={districts}
          style={{
            color: "blue",
            weight: 2,
            fillColor: "lightblue",
            fillOpacity: 0.4
          }}
        />
      )}

      {/* Weather event markers */}
      {weatherData && weatherData.map(event => (
        <Marker key={event.id} position={[event.lat, event.lng]}>
          <Popup>
            <strong>{event.type}</strong><br />
            District: {event.district}<br />
            Time: {event.timestamp}<br />
            Description: {event.description}<br />
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
}
