// Service Worker for Bhutan Weather Events
// Provides offline caching and performance improvements

const CACHE_NAME = 'bhutan-weather-events-v1';
const STATIC_CACHE_NAME = 'static-v1';
const DYNAMIC_CACHE_NAME = 'dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/index.html',
  '/src/main.jsx',
  '/src/App.jsx',
  '/src/App.css',
  '/maps/bhutan.geojson',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
  'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
  
  // Force the waiting service worker to become the active service worker
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Ensure the service worker takes control immediately
  self.clients.claim();
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Handle different types of requests
  if (request.method !== 'GET') {
    return; // Only handle GET requests
  }
  
  // Handle Google Sheets API requests
  if (url.hostname === 'docs.google.com' && url.pathname.includes('/gviz/tq')) {
    event.respondWith(handleGoogleSheetsRequest(request));
    return;
  }
  
  // Handle static files
  if (STATIC_FILES.some(file => request.url.includes(file))) {
    event.respondWith(handleStaticRequest(request));
    return;
  }
  
  // Handle other requests with network-first strategy
  event.respondWith(handleDynamicRequest(request));
});

// Handle Google Sheets requests with cache-first strategy (with TTL)
async function handleGoogleSheetsRequest(request) {
  const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  
  try {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      const cachedDate = new Date(cachedResponse.headers.get('sw-cached-date'));
      const now = new Date();
      
      // If cache is still fresh, return it
      if (now - cachedDate < CACHE_TTL) {
        console.log('Service Worker: Serving Google Sheets data from cache');
        return cachedResponse;
      }
    }
    
    // Fetch fresh data
    console.log('Service Worker: Fetching fresh Google Sheets data');
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Clone the response and add cache timestamp
      const responseToCache = networkResponse.clone();
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-date', new Date().toISOString());
      
      const modifiedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      // Cache the response
      cache.put(request, modifiedResponse.clone());
      return networkResponse;
    }
    
    // If network fails, return cached version if available
    if (cachedResponse) {
      console.log('Service Worker: Network failed, serving stale Google Sheets data');
      return cachedResponse;
    }
    
    throw new Error('No cached data available');
    
  } catch (error) {
    console.error('Service Worker: Error handling Google Sheets request', error);
    
    // Return a fallback response for Google Sheets
    return new Response(
      'google.visualization.Query.setResponse({"table":{"rows":[]}});',
      {
        headers: { 'Content-Type': 'text/javascript' },
        status: 200
      }
    );
  }
}

// Handle static files with cache-first strategy
async function handleStaticRequest(request) {
  try {
    const cache = await caches.open(STATIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('Service Worker: Serving static file from cache', request.url);
      return cachedResponse;
    }
    
    // If not in cache, fetch and cache
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.error('Service Worker: Error handling static request', error);
    throw error;
  }
}

// Handle dynamic requests with network-first strategy
async function handleDynamicRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('Service Worker: Network failed, trying cache for', request.url);
    
    // Fallback to cache
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If no cache available, return offline page or error
    if (request.destination === 'document') {
      return new Response(
        `<!DOCTYPE html>
        <html>
        <head>
          <title>Offline - Bhutan Weather Events</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline { color: #e74c3c; }
          </style>
        </head>
        <body>
          <h1 class="offline">You're Offline</h1>
          <p>Please check your internet connection and try again.</p>
          <button onclick="window.location.reload()">Retry</button>
        </body>
        </html>`,
        {
          headers: { 'Content-Type': 'text/html' },
          status: 200
        }
      );
    }
    
    throw error;
  }
}

// Handle background sync for offline data submission (future feature)
self.addEventListener('sync', (event) => {
  if (event.tag === 'weather-data-sync') {
    event.waitUntil(syncWeatherData());
  }
});

async function syncWeatherData() {
  // Placeholder for future offline data submission feature
  console.log('Service Worker: Syncing weather data...');
}

// Handle push notifications (future feature)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: 'weather-alert',
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'View Details'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('Service Worker: Loaded successfully');
