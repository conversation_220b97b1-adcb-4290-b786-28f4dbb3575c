/**
 * Deployment Configuration for Bhutan Weather Events
 * Supports multiple deployment targets and environments
 */

const deployConfig = {
  // Environment configurations
  environments: {
    development: {
      name: 'Development',
      url: 'http://localhost:5173',
      apiTimeout: 10000,
      cacheEnabled: false,
      debugMode: true
    },
    
    staging: {
      name: 'Staging',
      url: 'https://staging-weather.yourorg.gov.bt',
      apiTimeout: 8000,
      cacheEnabled: true,
      debugMode: true
    },
    
    production: {
      name: 'Production',
      url: 'https://weather.yourorg.gov.bt',
      apiTimeout: 5000,
      cacheEnabled: true,
      debugMode: false
    }
  },

  // Build configurations
  build: {
    outputDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Set to true for debugging
    minify: true,
    
    // Bundle splitting for better caching
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          leaflet: ['leaflet', 'react-leaflet'],
          utils: ['src/services/secureDataService.js']
        }
      }
    }
  },

  // Deployment targets
  targets: {
    // Netlify deployment
    netlify: {
      buildCommand: 'npm run build',
      publishDir: 'dist',
      redirects: [
        {
          from: '/*',
          to: '/index.html',
          status: 200
        }
      ],
      headers: [
        {
          for: '/maps/*',
          values: {
            'Cache-Control': 'public, max-age=31536000, immutable'
          }
        },
        {
          for: '/assets/*',
          values: {
            'Cache-Control': 'public, max-age=31536000, immutable'
          }
        }
      ]
    },

    // Vercel deployment
    vercel: {
      buildCommand: 'npm run build',
      outputDirectory: 'dist',
      rewrites: [
        {
          source: '/(.*)',
          destination: '/index.html'
        }
      ]
    },

    // GitHub Pages deployment
    githubPages: {
      buildCommand: 'npm run build',
      publishDir: 'dist',
      basePath: '/bhutan-weather-events' // Adjust based on repository name
    },

    // Traditional web server
    webServer: {
      buildCommand: 'npm run build',
      publishDir: 'dist',
      serverConfig: {
        // Apache .htaccess
        apache: `
RewriteEngine On
RewriteBase /

# Handle client-side routing
RewriteRule ^index\\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Cache static assets
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
</FilesMatch>

# Cache GeoJSON files
<FilesMatch "\\.geojson$">
  ExpiresActive On
  ExpiresDefault "access plus 1 month"
</FilesMatch>
        `,

        // Nginx configuration
        nginx: `
location / {
  try_files $uri $uri/ /index.html;
}

# Cache static assets
location ~* \\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
  expires 1y;
  add_header Cache-Control "public, immutable";
}

# Cache GeoJSON files
location ~* \\.geojson$ {
  expires 1M;
  add_header Cache-Control "public";
}

# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
        `
      }
    }
  },

  // Performance optimizations
  performance: {
    // Image optimization
    images: {
      formats: ['webp', 'avif', 'png', 'jpg'],
      sizes: [640, 768, 1024, 1280, 1920],
      quality: 80
    },

    // Code splitting
    codeSplitting: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 244000
    },

    // Compression
    compression: {
      gzip: true,
      brotli: true
    }
  },

  // Monitoring and analytics
  monitoring: {
    // Google Analytics (optional)
    googleAnalytics: {
      enabled: false,
      trackingId: 'GA_TRACKING_ID'
    },

    // Error tracking (optional)
    sentry: {
      enabled: false,
      dsn: 'SENTRY_DSN'
    },

    // Performance monitoring
    webVitals: {
      enabled: true,
      endpoint: '/api/web-vitals'
    }
  },

  // Security configurations
  security: {
    // Content Security Policy
    csp: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'", 'https://docs.google.com'],
      'style-src': ["'self'", "'unsafe-inline'", 'https://unpkg.com'],
      'img-src': ["'self'", 'data:', 'https:', 'blob:'],
      'connect-src': ["'self'", 'https://docs.google.com', 'https://*.tile.openstreetmap.org'],
      'font-src': ["'self'", 'https://cdnjs.cloudflare.com']
    },

    // HTTPS enforcement
    httpsRedirect: true,

    // HSTS header
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }
};

// Export configuration based on environment
const getConfig = (env = 'production') => {
  const baseConfig = deployConfig.environments[env] || deployConfig.environments.production;
  
  return {
    ...deployConfig,
    currentEnv: baseConfig,
    isProduction: env === 'production',
    isDevelopment: env === 'development'
  };
};

// Node.js export
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { deployConfig, getConfig };
}

// ES6 export
export { deployConfig, getConfig };

// Browser global
if (typeof window !== 'undefined') {
  window.deployConfig = deployConfig;
}
