# Bhutan Weather Events Monitor

A real-time weather event tracking system for Bhutan, displaying weather events across all 20 districts on an interactive map. Built for organizational deployment with professional features and security considerations.

## 🌟 Features

- **Real-time Weather Event Tracking**: Live data from Google Sheets integration
- **Interactive Map**: Pan, zoom, and click on districts and events
- **Professional UI**: Loading states, error handling, and responsive design
- **Security**: Input validation, XSS protection, and secure data handling
- **Performance**: Caching, retry logic, and optimized rendering
- **Accessibility**: Screen reader support and keyboard navigation

## 🗺️ What's Included

- **District Boundaries**: All 20 Bhutan districts with accurate GeoJSON data
- **Event Markers**: Color-coded markers for different weather event types
- **Event Details**: Popups with comprehensive event information
- **Auto-refresh**: Automatic data updates every 5 minutes
- **Legend**: Visual guide for event types and district status
- **Error Handling**: Graceful fallbacks and user-friendly error messages

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Google Sheets with weather event data

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd bhutan-weather-events
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your Google Sheets ID and organization details
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Open in browser**
   ```
   http://localhost:5173
   ```

## 📊 Data Setup

### Google Sheets Format

Your Google Sheets should have the following columns:

| Column | Description | Example |
|--------|-------------|---------|
| A | Event ID | EVT001 |
| B | Event Type | Flood, Landslide, Drought, Storm, Earthquake |
| C | District | Thimphu, Paro, Punakha, etc. |
| D | Coordinates | 27.4728,89.6393 |
| E | Timestamp | Date(2025,0,8,14,30) |
| F | Description | Heavy rainfall causing flooding |
| G | Photo URL | https://example.com/photo.jpg |
| H | Reporter | Weather Station Alpha |

### Making Sheets Public

1. Open your Google Sheet
2. Click "Share" → "Change to anyone with the link"
3. Set permission to "Viewer"
4. Copy the Sheet ID from the URL

## 🔧 Configuration

### Environment Variables

```env
# Required
VITE_GOOGLE_SHEET_ID=your_google_sheet_id_here

# Optional - Organization Branding
VITE_ORG_NAME=Your Organization Name
VITE_ORG_LOGO_URL=/path/to/logo.png
VITE_CONTACT_EMAIL=<EMAIL>

# Optional - Map Settings
VITE_MAP_CENTER_LAT=27.5
VITE_MAP_CENTER_LNG=90.5
VITE_MAP_DEFAULT_ZOOM=8.5
```

## 🏗️ Production Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Static Hosting

The built files in `dist/` can be deployed to:

- **Netlify**: Drag and drop the `dist` folder
- **Vercel**: Connect your GitHub repository
- **GitHub Pages**: Use GitHub Actions workflow
- **Traditional Web Server**: Upload `dist` contents to web root

### Environment Setup

1. **Create production environment file**
2. **Set up monitoring and logging**
3. **Configure CDN for better performance**
4. **Set up SSL certificate**

## 🔒 Security Considerations

- ✅ Input validation and sanitization
- ✅ XSS protection
- ✅ Secure API calls with retry logic
- ✅ Error handling without exposing sensitive data
- ✅ Environment variable management

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🛠️ Development

### Project Structure

```
src/
├── components/          # React components
│   ├── WeatherMap.jsx   # Main map component
│   ├── LoadingSpinner.jsx
│   ├── ErrorMessage.jsx
│   └── MapLegend.jsx
├── services/            # Data services
│   └── secureDataService.js
├── App.jsx             # Main app component
└── main.jsx            # Entry point

public/
└── maps/
    └── bhutan.geojson  # District boundaries
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For technical support or questions:
- Email: [your-support-email]
- Documentation: [link-to-docs]
- Issues: [GitHub Issues URL]
