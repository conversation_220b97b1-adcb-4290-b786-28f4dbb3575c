import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Build configuration
  build: {
    outDir: 'dist',
    sourcemap: false, // Set to true for debugging in production
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          leaflet: ['leaflet', 'react-leaflet']
        }
      }
    }
  },

  // Development server configuration
  server: {
    port: 5173,
    host: true, // Allow external connections
    open: true  // Auto-open browser
  },

  // Preview server configuration
  preview: {
    port: 4173,
    host: true
  },

  // Environment variables prefix
  envPrefix: 'VITE_',

  // Base URL for deployment (change if deploying to subdirectory)
  base: '/',

  // Define global constants
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
})
